<template>
  <div>
    <div class="page-container">
      <main class="page-content">
        <div class="list-container">
        <div class="form-header">
          <i class="fas fa-file-alt"></i>
          <h2>{{ mainTitle }}</h2>
          <p>{{subTitle}}</p>
        </div>

        <!-- 加载状态和错误提示 -->
        <LoadingIndicator :show="isLoading" text="正在加载资料..." />
        <ErrorMessage
          :show="hasError && !isLoading"
          type="warning"
          :message="errorMessage"
          :show-retry="true"
          @retry="refreshData"
          @close="clearError"
        />

        <!-- 资料分类 -->
        <div class="material-categories">
          <button
            class="category-btn"
            :class="{ active: activeCategory === 'all' }"
            @click="switchCategory('all')"
          >
            全部
          </button>
          <button
            class="category-btn"
            :class="{ active: activeCategory === 'presentation' }"
            @click="switchCategory('presentation')"
          >
            演讲资料
          </button>
          <button
            class="category-btn"
            :class="{ active: activeCategory === 'document' }"
            @click="switchCategory('document')"
          >
            会议文档
          </button>
          <button
            class="category-btn"
            :class="{ active: activeCategory === 'handbook' }"
            @click="switchCategory('handbook')"
          >
            参会手册
          </button>
        </div>

        <!-- 资料列表 -->
        <div class="materials-list" id="materialsList">
          <!-- 动态渲染所有资料 -->
          <div class="material-item" v-for="material in filteredMaterials" :key="material.id">
            <div class="material-header">
              <div class="material-icon">
                <i :class="getFileIcon(material.url)"></i>
              </div>
              <div class="material-info">
                <h3>{{ material.title }}<span class="material-tag">{{ getFileFormatter(material.url) }}</span></h3>
                <div class="material-meta">
                  <span><i class="fas fa-calendar"></i> {{ material.date }}</span>
                  <!-- <span><i class="fas fa-file"></i> {{ material.size }}</span> -->
                  <span><i class="fas fa-download"></i> {{ material.downloads? material.downloads : 0 }}次</span>
                </div>
              </div>
            </div>
            <div class="material-description">
              {{ material.description }}
            </div>
            <div class="material-actions">
              <button class="action-btn download-btn" @click="downloadMaterial(material.id)" :disabled="!material.url">
                <i class="fas fa-download"></i>
                下载
              </button>
              <button class="action-btn preview-btn" @click="previewMaterial(material.id)" :disabled="!material.url">
                <i class="fas fa-eye"></i>
                预览
              </button>
            </div>
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="materials-stats">
          <div class="stat-item">
            <i class="fas fa-file-alt"></i>
            <span>总资料数：<strong id="totalMaterials">{{ totalMaterials }}</strong></span>
          </div>
          <div class="stat-item">
            <i class="fas fa-download"></i>
            <span>下载次数：<strong id="totalDownloads">{{ totalDownloads }}</strong></span>
          </div>
        </div>
      </div>
    </main>
    </div>

    <!-- PDF预览模态框 - 放在页面容器外部 -->
    <PdfModal
      :visible="pdfModalVisible"
      :pdf-url="currentPdfUrl"
      :title="currentPdfTitle"
      @close="closePdfModal"
      class="materials-pdf-modal"
    />
  </div>
</template>
<script>
import { getList } from '@/api/materials/materials';
import { dataTransformers } from '@/utils/apiHelper';
import apiMixin from '@/mixins/apiMixin';
import LoadingIndicator from '@/components/LoadingIndicator.vue';
import ErrorMessage from '@/components/ErrorMessage.vue';
import { getDictionary } from '@/api/system/dictbiz';
import PdfModal from '@/components/PdfModal.vue';
import { downloadFileBlob } from '@/utils/util';

export default {
  name: 'Materials',
  mixins: [apiMixin],
  components: {
    LoadingIndicator,
    ErrorMessage,
    PdfModal
  },
  data() {
    return {
      mainTitle:'',
      subTitle:'',
      materials: [],
      activeCategory: 'all',
      dataSource: 'unknown',
      responseTime: 0,
      // PDF预览相关
      pdfModalVisible: false,
      currentPdfUrl: '',
      currentPdfTitle: '',
      // 默认资料数据 - 按照demo样式重新组织
      defaultMaterialsData: [
        {
          id: '1',
          title: '数智攀登管理跃升主题演讲',
          category: 'presentation',
          type: 'PPT',
          icon: 'fas fa-file-powerpoint',
          date: '2025-09-15',
          size: '15.2MB',
          downloads: 156,
          description: '企业数字化转型与智能化管理的创新实践分享，包含最新的管理理念和实施方案。'
        },
        {
          id: '2',
          title: '企业管理现场会议程安排',
          category: 'document',
          type: 'PDF',
          icon: 'fas fa-file-pdf',
          date: '2025-09-10',
          size: '2.8MB',
          downloads: 234,
          description: '详细的会议日程安排，包含所有演讲主题、时间安排和会场信息。'
        },
        {
          id: '3',
          title: '参会人员手册',
          category: 'handbook',
          type: 'PDF',
          icon: 'fas fa-book',
          date: '2025-09-08',
          size: '8.5MB',
          downloads: 189,
          description: '参会须知、交通指南、住宿信息、联系方式等重要信息汇总。'
        }
      ]
    }
  },
  computed: {
    allMaterials() {
      // 合并API数据和默认数据，如果API数据为空则使用默认数据
      return this.materials.length > 0 ? this.materials : this.defaultMaterialsData;
    },
    filteredMaterials() {
      // 根据当前选择的分类过滤资料
      if (this.activeCategory === 'all') {
        return this.allMaterials;
      }
      return this.allMaterials.filter(material => material.category === this.activeCategory);
    },
    totalMaterials() {
      return this.allMaterials.length;
    },
    totalDownloads() {
      return this.allMaterials.reduce((sum, material) => sum + (material.downloads || 0), 0);
    }
  },
  async mounted() {
    await this.loadMaterialsData();
    await this.loadData();
  },
  methods: {
    async loadData() {
      const response = await getDictionary({
          code: 'hy_materials' // 字典编码，需要在后台配置
        });
        // 检查响应格式
        if (response && response.data && response.data.success) {
          const dictData = response.data.data;
          if (dictData && Array.isArray(dictData) && dictData.length > 0) {
            // 从字典数据中提取文本
          this.mainTitle = dictData.find(item => item.dictValue === '主标题').dictKey;
          this.subTitle= dictData.find(item => item.dictValue === '副标题').dictKey;
          } else {
            console.log('API返回数据为空');
          }
        } else {
          throw new Error('API响应格式不正确');
        }
    },
    /**
     * 加载资料数据
     */
    async loadMaterialsData() {
      const startTime = Date.now();

      try {
        console.log('开始加载资料数据...');

        // 直接调用API
        const response = await getList(1, 20, {});
        console.log('资料API响应:', response);

        // 检查响应格式
        if (response && response.data && response.data.success) {
          // 使用数据转换器处理数据
          const transformedData = dataTransformers.materials(response.data);
          console.log('转换后的资料数据:', transformedData);

          this.materials = transformedData;
          this.dataSource = 'api';
          this.hasError = false;
          this.errorMessage = '';
        } else {
          throw new Error('API响应格式不正确');
        }

        this.responseTime = Date.now() - startTime;

      } catch (error) {
        console.error('加载资料数据失败:', error);
        this.materials = this.defaultMaterialsData;
        this.dataSource = 'fallback';
        this.hasError = true;
        this.errorMessage = error.message || '数据加载失败，使用默认数据';
        this.responseTime = Date.now() - startTime;
      }
    },

    /**
     * 刷新资料数据
     */
    async refreshData() {
      await this.loadMaterialsData();
    },

    /**
     * 格式化资料数据
     */
    formatApiData(data, type) {
      if (type === 'array' && Array.isArray(data)) {
        return dataTransformers.materials(data);
      }
      return data;
    },

    /**
     * 切换分类
     */
    switchCategory(category) {
      this.activeCategory = category;
      console.log(`切换到分类: ${category}`);
    },

    /**
     * 获取文件格式
     */
    getFileFormatter(fileUrlOrType) {
      let fileType = fileUrlOrType;
      // 如果传入的是文件URL，则从URL中提取文件后缀
      if (fileUrlOrType && fileUrlOrType.includes('.')) {
        const extension = fileUrlOrType.split('.').pop().toLowerCase();

        // 根据文件后缀映射到标准类型
        const extensionMap = {
          'pdf': 'PDF',
          'ppt': 'PPT',
          'pptx': 'PPT',
          'xls': 'XLSX',
          'xlsx': 'XLSX',
          'doc': 'DOC',
          'docx': 'DOC',
          'zip': 'ZIP',
          'rar': 'ZIP'
        };

        fileType = extensionMap[extension] || fileUrlOrType;
        return fileType;
      }else{
        return 'PDF';
      }
    },
    /**
     * 获取文件图标
     */
    getFileIcon(fileUrlOrType) {
      let fileType = fileUrlOrType;
      // 如果传入的是文件URL，则从URL中提取文件后缀
      if (fileUrlOrType && fileUrlOrType.includes('.')) {
        const extension = fileUrlOrType.split('.').pop().toLowerCase();

        // 根据文件后缀映射到标准类型
        const extensionMap = {
          'pdf': 'PDF',
          'ppt': 'PPT',
          'pptx': 'PPT',
          'xls': 'XLSX',
          'xlsx': 'XLSX',
          'doc': 'DOC',
          'docx': 'DOC',
          'zip': 'ZIP',
          'rar': 'ZIP'
        };

        fileType = extensionMap[extension] || fileUrlOrType;
      }
      // 图标映射
      const iconMap = {
        'PDF': 'fas fa-file-pdf',
        'PPT': 'fas fa-file-powerpoint',
        'XLSX': 'fas fa-file-excel',
        'DOC': 'fas fa-file-word',
        'ZIP': 'fas fa-file-archive'
      };

      return iconMap[fileType] || 'fas fa-file';
    },

    /**
     * 下载资料
     */
    downloadMaterial(materialId) {
      const material = this.allMaterials.find(m => m.id === materialId);
      if (material) {
        console.log(`下载资料: ${material.title}`);

        if (!material.url) {
          if (this.$message) {
            this.$message.warning('该资料暂无下载链接');
          } else {
            alert('该资料暂无下载链接');
          }
          return;
        }

        try {
          // 增加下载次数
          material.downloads = (material.downloads || 0) + 1;

          // 获取文件名
          const fileName = material.title + '.pdf';

          // 使用工具函数下载文件
          downloadFileBlob(material.url, fileName);

          if (this.$message) {
            this.$message.success(`正在下载：${material.title}`);
          }
        } catch (error) {
          console.error('下载失败:', error);
          if (this.$message) {
            this.$message.error('下载失败，请稍后重试');
          } else {
            alert('下载失败，请稍后重试');
          }
        }
      }
    },

    /**
     * 预览资料
     */
    previewMaterial(materialId) {
      const material = this.allMaterials.find(m => m.id === materialId);
      if (material) {
        console.log(`预览资料: ${material.title}`);

        if (!material.url) {
          if (this.$message) {
            this.$message.warning('该资料暂无预览链接');
          } else {
            alert('该资料暂无预览链接');
          }
          return;
        }

        // 打开PDF预览模态框
        this.currentPdfUrl = material.url;
        this.currentPdfTitle = material.title;
        this.pdfModalVisible = true;
      }
    },

    /**
     * 关闭PDF预览模态框
     */
    closePdfModal() {
      this.pdfModalVisible = false;
      this.currentPdfUrl = '';
      this.currentPdfTitle = '';
    },

    /**
     * 刷新数据
     */
    async refreshData() {
      await this.loadMaterialsData();
    },

    /**
     * 清除错误
     */
    clearError() {
      this.hasError = false;
      this.errorMessage = '';
    }
  }
}
</script>

<style scoped>
/* 页面通用样式 */
.page-container {
    max-width: 375px;
    margin: 0 auto;
    min-height: 100vh;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%);
}

/* 页面内容 */
.page-content {
    margin-top: 20px;
    min-height: calc(100vh - 80px);
}

/* 表单容器 */
.list-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    animation: slideInUp 0.6s ease forwards;
}

.form-header {
    text-align: center;
    margin-bottom: 30px;
}

.form-header i {
    font-size: 48px;
    color: #4682B4;
    margin-bottom: 15px;
}

.form-header h2 {
    color: #333;
    font-size: 24px;
    margin-bottom: 10px;
}

.form-header p {
    color: #666;
    font-size: 14px;
}

/* 资料分类按钮样式 */
.material-categories {
    display: flex;
    gap: 10px;
    margin: 20px 0;
    overflow-x: auto;
    padding: 5px 0;
}

.category-btn {
    background: rgba(70, 130, 180, 0.1);
    border: 1px solid rgba(70, 130, 180, 0.3);
    color: #4682B4;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.category-btn.active,
.category-btn:hover {
    background: #4682B4;
    color: white;
}

/* 资料列表样式 - 完全按照demo重新设计 */
.materials-list {
    margin: 20px 0;
}

.material-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border-left: 4px solid #4682B4;
    transition: all 0.3s ease;
}

.material-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.material-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.material-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #4682B4, #1E90FF);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.material-icon i {
    color: white;
    font-size: 20px;
}

.material-info {
    flex: 1;
}

.material-info h3 {
    color: #333;
    font-size: 16px;
    margin-bottom: 5px;
}

.material-meta {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #666;
}

.material-meta span {
    display: flex;
    align-items: center;
    gap: 3px;
}

.material-description {
    color: #666;
    font-size: 14px;
    line-height: 1.4;
    margin: 15px 0;
}

.material-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 8px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.download-btn {
    background: linear-gradient(135deg, #4682B4, #1E90FF);
    color: white;
}

.preview-btn {
    background: rgba(70, 130, 180, 0.1);
    color: #4682B4;
    border: 1px solid rgba(70, 130, 180, 0.3);
}

.action-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.material-tag {
    background: rgba(70, 130, 180, 0.1);
    color: #4682B4;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    margin-left: 10px;
}

/* 统计信息样式 */
.materials-stats {
    display: flex;
    justify-content: space-around;
    background: white;
    border-radius: 12px;
    padding: 15px;
    margin-top: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
}

.stat-item i {
    color: #4682B4;
}

.stat-item strong {
    color: #333;
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* PDF模态框特殊样式 - 确保全屏显示 */
.materials-pdf-modal {
    z-index: 999999 !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* 覆盖PDF模态框根元素样式 - 使用深度选择器 */
.materials-pdf-modal :deep(.pdf-modal) {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 999999 !important;
}

/* 覆盖PDF模态框内容区域样式，使其更大 */
.materials-pdf-modal :deep(.pdf-modal-content) {
    width: 95vw !important;
    height: 95vh !important;
    max-width: none !important;
    max-height: none !important;
}

/* 移动端特殊处理 */
@media (max-width: 768px) {
    .materials-pdf-modal :deep(.pdf-modal-content) {
        width: 98vw !important;
        height: 98vh !important;
        border-radius: 4px !important;
    }
}

/* 响应式设计 */
@media (max-width: 480px) {


    .list-container {
        padding: 15px;
    }

    .material-item {
        padding: 12px;
    }

    .material-icon {
        width: 40px;
        height: 40px;
        margin-right: 12px;
    }

    .material-icon i {
        font-size: 20px;
    }

    .material-info h3 {
        font-size: 14px;
    }

    .material-info p {
        font-size: 12px;
    }

    .material-actions {
        flex-direction: row;
    }
}
</style>