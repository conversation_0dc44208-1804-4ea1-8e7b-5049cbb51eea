/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.userschedule.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;


import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.hy.userschedule.pojo.entity.UserScheduleEntity;
import org.springblade.modules.hy.userschedule.pojo.dto.UserScheduleDTO;
import org.springblade.modules.hy.userschedule.pojo.vo.UserScheduleVO;
import org.springblade.modules.hy.userschedule.pojo.excel.UserScheduleExcel;
import org.springblade.modules.hy.userschedule.pojo.excel.UserScheduleImportExcel;
import org.springblade.modules.hy.userschedule.pojo.dto.UserScheduleImportResultDTO;
import org.springframework.web.multipart.MultipartFile;
import org.springblade.modules.hy.userschedule.wrapper.UserScheduleWrapper;
import org.springblade.modules.hy.userschedule.service.IUserScheduleService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.core.tool.utils.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.util.List;

import static org.springblade.core.secure.utils.AuthUtil.getUserId;

/**
 * 用户日程信息表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@RestController
@AllArgsConstructor
@RequestMapping("hy/user-schedule")
@Tag(name = "用户日程信息表", description = "用户日程信息表接口")
public class UserScheduleController extends BladeController {

	private final IUserScheduleService userScheduleService;
	private final IUserService userService;

	/**
	 * 用户日程信息表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入userSchedule")
	public R<UserScheduleVO> detail(UserScheduleEntity userSchedule) {
		UserScheduleEntity detail = userScheduleService.getOne(Condition.getQueryWrapper(userSchedule));
		return R.data(UserScheduleWrapper.build().entityVO(detail));
	}

	/**
	 * 根据用户ID获取日程信息
	 */
	@GetMapping("/by-user/{userId}")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "根据用户ID获取日程信息", description = "传入userId")
	public R<UserScheduleVO> getByUserId(@PathVariable Long userId) {
		UserScheduleVO userSchedule = userScheduleService.getByUserId(userId);
		return R.data(userSchedule);
	}

	/**
	 * 获取当前用户的日程信息
	 */
	@GetMapping("/current")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "获取当前用户的日程信息")
	public R<UserScheduleVO> getCurrentUserSchedule() {
		Long userId = getUserId();
		UserScheduleVO userSchedule = userScheduleService.getByUserId(userId);
		return R.data(userSchedule);
	}

	/**
	 * 用户日程信息表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "分页", description = "传入userSchedule")
	public R<IPage<UserScheduleVO>> list(UserScheduleVO userSchedule, Query query) {
		IPage<UserScheduleVO> pages = userScheduleService.selectUserSchedulePage(Condition.getPage(query), userSchedule);
		return R.data(pages);
	}

	/**
	 * 用户日程信息表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "新增", description = "传入userSchedule")
	public R save(@Valid @RequestBody UserScheduleEntity userSchedule) {
		return R.status(userScheduleService.save(userSchedule));
	}

	/**
	 * 用户日程信息表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "修改", description = "传入userSchedule")
	public R update(@Valid @RequestBody UserScheduleEntity userSchedule) {
		return R.status(userScheduleService.updateById(userSchedule));
	}

	/**
	 * 保存或更新当前用户的日程信息
	 */
	@PostMapping("/save-current")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "保存或更新当前用户的日程信息", description = "传入userSchedule")
	public R saveCurrentUserSchedule(@Valid @RequestBody UserScheduleEntity userSchedule) {
		userSchedule.setUserId(getUserId());
		return R.status(userScheduleService.saveOrUpdateByUserId(userSchedule));
	}

	/**
	 * 用户日程信息表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 8)
	@Operation(summary = "新增或修改", description = "传入userScheduleDTO")
	public R submit(@Valid @RequestBody UserScheduleDTO userScheduleDTO) {
		// 根据用户姓名查找用户ID
		if (StringUtil.isNotBlank(userScheduleDTO.getUserRealName())) {
			LambdaQueryWrapper<User> userQuery = new LambdaQueryWrapper<>();
			userQuery.eq(User::getRealName, userScheduleDTO.getUserRealName());
			User user = userService.getOne(userQuery);
			if (user != null) {
				userScheduleDTO.setUserId(user.getId());
			}
		}

		// 如果有用户ID，同时更新用户信息
		if (userScheduleDTO.getUserId() != null) {
			User user = userService.getById(userScheduleDTO.getUserId());
			if (user != null) {
				boolean userUpdated = false;

				// 更新用户手机号
				if (StringUtil.isNotBlank(userScheduleDTO.getUserPhone()) &&
					!userScheduleDTO.getUserPhone().equals(user.getPhone())) {
					user.setPhone(userScheduleDTO.getUserPhone());
					userUpdated = true;
				}

				// 更新用户工号
				if (StringUtil.isNotBlank(userScheduleDTO.getUserEmployeeNumber()) &&
					!userScheduleDTO.getUserEmployeeNumber().equals(user.getEmployeeNumber())) {
					user.setEmployeeNumber(userScheduleDTO.getUserEmployeeNumber());
					userUpdated = true;
				}

				// 更新用户房号
				if (StringUtil.isNotBlank(userScheduleDTO.getUserRoomNumber()) &&
					!userScheduleDTO.getUserRoomNumber().equals(user.getRoomNumber())) {
					user.setRoomNumber(userScheduleDTO.getUserRoomNumber());
					userUpdated = true;
				}

				// 更新用户会议座位号
				if (StringUtil.isNotBlank(userScheduleDTO.getUserMeetingSeatNumber()) &&
					!userScheduleDTO.getUserMeetingSeatNumber().equals(user.getMeetingSeatNumber())) {
					user.setMeetingSeatNumber(userScheduleDTO.getUserMeetingSeatNumber());
					userUpdated = true;
				}

				// 如果有更新，保存用户信息
				if (userUpdated) {
					userService.updateById(user);
				}
			}
		}

		// 手动转换DTO为Entity
		UserScheduleEntity userSchedule = new UserScheduleEntity();
		if (userScheduleDTO.getId() != null) {
			userSchedule.setId(userScheduleDTO.getId());
		}
		userSchedule.setUserId(userScheduleDTO.getUserId());
		userSchedule.setScheduleContent(userScheduleDTO.getScheduleContent());
		userSchedule.setDiningInfo(userScheduleDTO.getDiningInfo());
		userSchedule.setAccommodationInfo(userScheduleDTO.getAccommodationInfo());
		userSchedule.setUserRealName(userScheduleDTO.getUserRealName());

		return R.status(userScheduleService.saveOrUpdate(userSchedule));
	}

	/**
	 * 用户日程信息表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@RequestParam String ids) {
		return R.status(userScheduleService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 用户日程信息表 导出
	 */
	@GetMapping("/export-user-schedule")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "导出", description = "传入userSchedule")
	public void exportUserSchedule(UserScheduleEntity userSchedule, HttpServletResponse response) {
		QueryWrapper<UserScheduleEntity> queryWrapper = Condition.getQueryWrapper(userSchedule);
		// 添加关联查询条件
		if (StringUtil.isNotBlank(userSchedule.getUserRealName())) {
			queryWrapper.apply("EXISTS (SELECT 1 FROM blade_user u WHERE u.id = hy_user_schedule.user_id AND u.real_name LIKE {0})", "%" + userSchedule.getUserRealName() + "%");
		}
		// 支持按用户ID查询
		if (userSchedule.getUserId() != null) {
			queryWrapper.eq("user_id", userSchedule.getUserId());
		}
		List<UserScheduleExcel> list = userScheduleService.exportUserSchedule(queryWrapper);
		ExcelUtil.export(response, "用户日程信息表数据", "用户日程信息表数据", list, UserScheduleExcel.class);
	}

	/**
	 * Excel导入用户日程信息
	 */
	@PostMapping("/import-user-schedule")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "Excel导入", description = "上传Excel文件导入用户日程信息")
	public R<UserScheduleImportResultDTO> importUserSchedule(@RequestParam("file") MultipartFile file) {
		if (file.isEmpty()) {
			return R.fail("请选择要上传的文件");
		}

		// 检查文件类型
		String fileName = file.getOriginalFilename();
		if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
			return R.fail("请上传Excel格式的文件");
		}

		// 检查文件大小（限制为10MB）
		if (file.getSize() > 10 * 1024 * 1024) {
			return R.fail("文件大小不能超过10MB");
		}

		UserScheduleImportResultDTO result = userScheduleService.importUserSchedule(file);
		return R.data(result);
	}

	/**
	 * 下载导入模板
	 */
	@GetMapping("/download-template")
	@ApiOperationSupport(order = 11)
	@Operation(summary = "下载导入模板", description = "下载用户日程信息导入模板")
	public void downloadTemplate(HttpServletResponse response) {
		List<UserScheduleImportExcel> templateList = userScheduleService.getImportTemplate();
		ExcelUtil.export(response, "用户日程信息导入模板", "用户日程信息", templateList, UserScheduleImportExcel.class);
	}

}
