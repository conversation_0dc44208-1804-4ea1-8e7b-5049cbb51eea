/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chi<PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.userschedule.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 用户日程信息导入结果DTO
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
@Schema(description = "用户日程信息导入结果")
public class UserScheduleImportResultDTO implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 导入是否成功
	 */
	@Schema(description = "导入是否成功")
	private Boolean success;

	/**
	 * 总记录数
	 */
	@Schema(description = "总记录数")
	private Integer totalCount;

	/**
	 * 成功导入数量
	 */
	@Schema(description = "成功导入数量")
	private Integer successCount;

	/**
	 * 失败数量
	 */
	@Schema(description = "失败数量")
	private Integer failCount;

	/**
	 * 更新数量（用户信息更新）
	 */
	@Schema(description = "更新数量")
	private Integer updateCount;

	/**
	 * 新增数量（新用户）
	 */
	@Schema(description = "新增数量")
	private Integer insertCount;

	/**
	 * 错误信息列表
	 */
	@Schema(description = "错误信息列表")
	private List<String> errorMessages;

	/**
	 * 导入结果消息
	 */
	@Schema(description = "导入结果消息")
	private String message;

}
