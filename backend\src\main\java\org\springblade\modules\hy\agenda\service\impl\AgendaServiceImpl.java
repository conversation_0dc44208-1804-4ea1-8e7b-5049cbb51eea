/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.hy.agenda.service.impl;

import org.springblade.modules.hy.agenda.pojo.entity.AgendaEntity;
import org.springblade.modules.hy.agenda.pojo.vo.AgendaVO;
import org.springblade.modules.hy.agenda.excel.AgendaExcel;
import org.springblade.modules.hy.agenda.excel.AgendaImportExcel;
import org.springblade.modules.hy.agenda.mapper.AgendaMapper;
import org.springblade.modules.hy.agenda.service.IAgendaService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ArrayList;

/**
 * 会议议程表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Service
public class AgendaServiceImpl extends BaseServiceImpl<AgendaMapper, AgendaEntity> implements IAgendaService {

	@Override
	public IPage<AgendaVO> selectAgendaPage(IPage<AgendaVO> page, AgendaVO agenda) {
		return page.setRecords(baseMapper.selectAgendaPage(page, agenda));
	}


	@Override
	public List<AgendaExcel> exportAgenda(Wrapper<AgendaEntity> queryWrapper) {
		List<AgendaExcel> agendaList = baseMapper.exportAgenda(queryWrapper);
		//agendaList.forEach(agenda -> {
		//	agenda.setTypeName(DictCache.getValue(DictEnum.YES_NO, Agenda.getType()));
		//});
		return agendaList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void importAgenda(List<AgendaImportExcel> data, Boolean isCovered) {
		List<AgendaEntity> agendaList = new ArrayList<>();
		DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");
		DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

		data.forEach(agendaExcel -> {
			AgendaEntity agenda = new AgendaEntity();

			// 复制基本属性
			agenda.setTopic(agendaExcel.getTopic());
			agenda.setSpeaker(agendaExcel.getSpeaker());
			agenda.setVenue(agendaExcel.getVenue());
			agenda.setDescription(agendaExcel.getDescription());

			// 处理日期字段转换
			if (agendaExcel.getDate() != null && !agendaExcel.getDate().trim().isEmpty()) {
				try {
					agenda.setDate(LocalDate.parse(agendaExcel.getDate(), dateFormatter));
				} catch (Exception e) {
					// 如果解析失败，可以记录日志或设置默认值
					agenda.setDate(null);
				}
			}

			// 处理时间字段转换
			if (agendaExcel.getStartTime() != null && !agendaExcel.getStartTime().trim().isEmpty()) {
				try {
					agenda.setStartTime(LocalTime.parse(agendaExcel.getStartTime(), timeFormatter));
				} catch (Exception e) {
					// 如果解析失败，可以记录日志或设置默认值
					agenda.setStartTime(null);
				}
			}

			if (agendaExcel.getEndTime() != null && !agendaExcel.getEndTime().trim().isEmpty()) {
				try {
					agenda.setEndTime(LocalTime.parse(agendaExcel.getEndTime(), timeFormatter));
				} catch (Exception e) {
					// 如果解析失败，可以记录日志或设置默认值
					agenda.setEndTime(null);
				}
			}

			agendaList.add(agenda);
		});

		// 只支持新增，不覆盖现有数据
		this.saveBatch(agendaList);
	}

}
