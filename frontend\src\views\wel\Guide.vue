<template>
  <div class="page-container">
    <main class="page-content">
      <div class="list-container">
        <div class="form-header">
          <i class="fas fa-book-open"></i>
          <h2>{{ mainTitle }}</h2>
          <p>{{subTitle}}</p>
        </div>

        <!-- 指南分类 -->
        <div class="guide-categories">
          <button
            class="guide-tab"
            :class="{ active: activeTab === 'basic' }"
            @click="switchTab('basic')"
          >
            基本信息
          </button>
          <button
            class="guide-tab"
            :class="{ active: activeTab === 'schedule' }"
            @click="switchTab('schedule')"
          >
            日程安排
          </button>
          <button
            class="guide-tab"
            :class="{ active: activeTab === 'location' }"
            @click="switchTab('location')"
          >
            交通住宿
          </button>
          <button
            class="guide-tab"
            :class="{ active: activeTab === 'rules' }"
            @click="switchTab('rules')"
          >
            注意事项
          </button>
        </div>

        <!-- 基本信息 -->
        <div class="guide-content" v-show="activeTab === 'basic'">
          <div class="guide-section">
            <h3><i class="fas fa-info-circle"></i> 会议基本信息</h3>
            <div class="info-card">
              <div class="info-row">
                <strong>会议名称：</strong>
                <span>{{ meetingInfo.meetingName }}</span>
              </div>
              <div class="info-row">
                <strong>主办单位：</strong>
                <span>{{ meetingInfo.organizer }}</span>
              </div>
              <div class="info-row">
                <strong>会议时间：</strong>
                <span>{{ meetingInfo.meetingTime }}</span>
              </div>
              <div class="info-row">
                <strong>会议地点：</strong>
                <span>{{ meetingInfo.meetingLocation }}</span>
              </div>
              <div class="info-row">
                <strong>参会人数：</strong>
                <span>{{ meetingInfo.participantCount }}</span>
              </div>
            </div>
          </div>

          <div class="guide-section">
            <h3><i class="fas fa-users"></i> 参会对象</h3>
            <div class="participant-list">
              <div v-for="participant in participants" :key="participant.name" class="participant-item">
                <i :class="participant.icon"></i>
                <span>{{ participant.name }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 日程安排 -->
        <div class="guide-content" v-show="activeTab === 'schedule'">
          <div class="guide-section">
            <h3><i class="fas fa-calendar-alt"></i> 详细日程</h3>

            <!-- 动态渲染议程数据 -->
            <div v-if="agendaList && agendaList.length > 0">
              <!-- 按日期分组显示 -->
              <div v-for="(dayAgenda, date) in groupedAgenda" :key="date" class="schedule-day">
                <h4>{{ formatDate(date) }}</h4>
                <div class="schedule-list">
                  <div v-for="agenda in dayAgenda" :key="agenda.id" class="schedule-item">
                    <div class="time">{{ agenda.time }}</div>
                    <div class="content">
                      <div class="topic">{{ agenda.topic }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 无数据时显示默认内容 -->
            <div v-else class="no-data">
              <i class="fas fa-calendar-times"></i>
              <p>暂无议程数据</p>
            </div>
          </div>
        </div>

        <!-- 交通住宿 -->
        <div class="guide-content" v-show="activeTab === 'location'">
          <div class="guide-section">
            <h3><i class="fas fa-map-marker-alt"></i> 会议地点</h3>
            <div class="location-card">
              <div class="location-info">
                <h4>广东烟草大厦会议中心</h4>
                <p>地址：广州市天河区珠江新城</p>
                <p>电话：020-12345678</p>
              </div>
              <button class="map-btn" @click="openMap">
                <i class="fas fa-map"></i>
                查看地图
              </button>
            </div>
          </div>

          <div class="guide-section">
            <h3><i class="fas fa-car"></i> 交通指南</h3>
            <div class="transport-options">
              <div v-for="transport in transportInfo" :key="transport.type" class="transport-item">
                <i :class="transport.icon"></i>
                <div>
                  <strong>{{ transport.type }}</strong>
                  <p>{{ transport.description }}</p>
                </div>
              </div>
            </div>
          </div>

          <div class="guide-section">
            <h3><i class="fas fa-bed"></i> 住宿推荐</h3>
            <div class="hotel-list">
              <div v-for="hotel in hotelList" :key="hotel.name" class="hotel-item">
                <h4>{{ hotel.name }}</h4>
                <p>{{ hotel.description }}</p>
                <p v-if="hotel.phone">联系电话：{{ hotel.phone }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 注意事项 -->
        <div class="guide-content" v-show="activeTab === 'rules'">
          <div class="guide-section">
            <h3><i class="fas fa-exclamation-triangle"></i> 重要提醒</h3>
            <div class="rules-list">
              <div v-for="notice in importantNotices" :key="notice.title" class="rule-item">
                <i class="fas fa-exclamation-circle"></i>
                <div>
                  <strong>{{ notice.title }}</strong>
                  <p>{{ notice.content }}</p>
                </div>
              </div>
            </div>
          </div>

          <div class="guide-section">
            <h3><i class="fas fa-phone"></i> 紧急联系</h3>
            <div class="contact-emergency">
              <div v-for="contact in emergencyContacts" :key="contact.title" class="emergency-item">
                <strong>{{ contact.title }}</strong>
                <span>{{ contact.contact }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 下载指南 -->
        <div class="download-section">
          <button class="submit-btn" @click="downloadGuide">
            <i class="fas fa-download"></i>
            下载完整指南
          </button>
        </div>
      </div>
    </main>
  </div>
</template>
<script>
import { getList } from '@/api/guide/guide';
import { getList as getAgendaList } from '@/api/agenda/agenda';
import { dataTransformers } from '@/utils/apiHelper';
import { getDictionary } from '@/api/system/dictbiz'

export default {
  name: 'Guide',
  data() {
    return {
      mainTitle:'',
      subTitle:'',
      activeTab: 'basic', // 默认显示基本信息
      guides: [],
      agendaList: [], // 议程数据
      dataSource: 'unknown',
      responseTime: 0,
      defaultGuidesData: [],
      guidePdfUrl: '', // 参会指南PDF的URL
      // 会议基本信息
      meetingInfo: {
        meetingName: '数智攀登，管理跃升 - 企业管理现场会',
        organizer: '广东烟草广州市有限公司',
        meetingTime: '2025年9月15日-16日',
        meetingLocation: '广东烟草大厦会议中心',
        participantCount: '约120人'
      },
      // 参会对象数据
      participants: [
        { icon: 'fas fa-crown', name: '公司领导层' },
        { icon: 'fas fa-user-tie', name: '各部门负责人' },
        { icon: 'fas fa-users', name: '管理人员' },
        { icon: 'fas fa-star', name: '特邀嘉宾' }
      ],
      // 交通信息数据
      transportInfo: [
        { icon: 'fas fa-subway', type: '地铁', description: '3号线/5号线珠江新城站，A出口步行5分钟' },
        { icon: 'fas fa-bus', type: '公交', description: '多路公交车可达，珠江新城站下车' },
        { icon: 'fas fa-car', type: '自驾', description: '大厦地下停车场，收费标准：10元/小时' }
      ],
      // 住宿推荐数据
      hotelList: [
        { name: '广州大酒店', description: '距离会场：步行3分钟', phone: '020-87654321' },
        { name: '珠江宾馆', description: '距离会场：步行8分钟', phone: '020-87654322' }
      ],
      // 重要提醒数据
      importantNotices: [
        { title: '着装要求', content: '建议商务正装，体现专业形象' },
        { title: '准时参会', content: '请提前15分钟到达会场，避免迟到影响会议进程' },
        { title: '携带证件', content: '请携带身份证或工作证，用于会议签到' }
      ],
      // 紧急联系数据
      emergencyContacts: [
        { title: '会务组联系人：', contact: '张先生 138-0000-0000' },
        { title: '技术支持：', contact: '李女士 139-0000-0000' },
        { title: '医疗急救：', contact: '120' }
      ]
    }
  },

  computed: {
    /**
     * 按日期分组的议程数据，并按时间顺序排序
     */
    groupedAgenda() {
      const grouped = {};

      // 按日期分组
      this.agendaList.forEach(item => {
        const date = item.date || '2025-09-15';
        if (!grouped[date]) {
          grouped[date] = [];
        }
        grouped[date].push(item);
      });

      // 按时间排序每一天的议程
      Object.keys(grouped).forEach(date => {
        grouped[date].sort((a, b) => {
          // 提取开始时间进行比较
          const timeA = this.extractStartTime(a.time);
          const timeB = this.extractStartTime(b.time);
          return timeA.localeCompare(timeB);
        });
      });

      // 将分组对象转换为按日期排序的数组
      const sortedDates = Object.keys(grouped).sort((a, b) => {
        // 按日期排序，确保第一天在前
        return new Date(a).getTime() - new Date(b).getTime();
      });

      // 返回按日期排序的对象
      const sortedGrouped = {};
      sortedDates.forEach(date => {
        sortedGrouped[date] = grouped[date];
      });

      return sortedGrouped;
    }
  },

  async mounted() {
    console.log('Guide组件开始挂载...');
    // 保存默认数据
    this.defaultGuidesData = [...this.guides];

    try {
      console.log('开始加载指南数据...');
      await this.loadGuidesData();

      console.log('开始加载议程数据...');
      await this.loadAgendaData();

      console.log('开始加载字典数据...');
      await this.loadData();

      console.log('开始加载参会指南PDF链接...');
      await this.loadGuidePdfUrl();

      console.log('Guide组件挂载完成');
    } catch (error) {
      console.error('Guide组件挂载过程中出错:', error);
    }
  },
  methods: {
    async loadData() {
      try {
        // 获取标题数据
        await this.loadTitleData();

        // 获取会议基本信息
        await this.loadMeetingInfoData();

      } catch (error) {
        console.error('加载数据失败:', error);
        this.mainTitle = '参会指南';
        this.subTitle = '企业管理现场会参会须知';
      }
    },

    /**
     * 加载标题数据
     */
    async loadTitleData() {
      try {
        const response = await getDictionary({
          code: 'hy_guide' // 字典编码，获取标题
        });

        if (response && response.data && response.data.success) {
          const dictData = response.data.data;
          console.log('dictData:',dictData)
          if (dictData && Array.isArray(dictData) && dictData.length > 0) {
            console.log('获取到标题数据:', dictData);

            // 从字典数据中提取标题
            const mainTitleItem = dictData.find(item => item.dictValue === '主标题');
            if (mainTitleItem) {
              this.mainTitle = mainTitleItem.dictKey;
            }

            const subTitleItem = dictData.find(item => item.dictValue === '副标题');
            if (subTitleItem) {
              this.subTitle = subTitleItem.dictKey;
            }

            // 提取参会对象数据
            this.extractParticipants(dictData);

            // 提取交通信息数据
            this.extractTransportInfo(dictData);

            // 提取住宿推荐数据
            this.extractHotelInfo(dictData);

            // 提取重要提醒数据
            this.extractImportantNotices(dictData);

            // 提取紧急联系数据
            this.extractEmergencyContacts(dictData);
          }
        }
      } catch (error) {
        console.error('加载标题数据失败:', error);
      }
    },

    /**
     * 加载会议基本信息数据
     */
    async loadMeetingInfoData() {
      try {
        const response = await getDictionary({
          code: 'hy_prop' // 字典编码，获取会议属性
        });

        if (response && response.data && response.data.success) {
          const dictData = response.data.data;
          if (dictData && Array.isArray(dictData) && dictData.length > 0) {
            console.log('获取到会议信息数据:', dictData);

            // 从字典数据中提取会议信息
            this.extractMeetingInfo(dictData);
          } else {
            console.log('会议信息API返回数据为空');
          }
        } else {
          throw new Error('会议信息API响应格式不正确');
        }
      } catch (error) {
        console.error('加载会议信息失败:', error);
      }
    },

    /**
     * 从字典数据中提取参会对象
     */
    extractParticipants(dictData) {
      try {
        const participantItems = [];

        // 根据表格中的数据，按字典名称提取参会对象
        const participantMappings = [
          { dictKey: '参会对象', icon: 'fas fa-users', order: 1 },
          { dictKey: '1', icon: 'fas fa-crown', order: 2 },
          { dictKey: '2', icon: 'fas fa-user-tie', order: 3 },
          { dictKey: '3', icon: 'fas fa-users', order: 4 },
          { dictKey: '4', icon: 'fas fa-star', order: 5 }
        ];

        participantMappings.forEach(mapping => {
          const item = dictData.find(dict => dict.dictKey === mapping.dictKey);
          if (item && item.dictValue && mapping.dictKey !== '参会对象') {
            participantItems.push({
              icon: mapping.icon,
              name: item.dictValue,
              order: mapping.order
            });
          }
        });

        // 按顺序排序
        participantItems.sort((a, b) => a.order - b.order);

        if (participantItems.length > 0) {
          this.participants = participantItems;
          console.log('提取的参会对象:', this.participants);
        } else {
          console.log('未找到参会对象数据，使用默认数据');
        }

      } catch (error) {
        console.error('提取参会对象失败:', error);
      }
    },

    /**
     * 从字典数据中提取交通信息
     */
    extractTransportInfo(dictData) {
      try {
        const transportItems = [];

        // 根据表格中的数据提取交通信息
        const transportMappings = [
          { dictValue: '地铁', icon: 'fas fa-subway', type: '地铁' },
          { dictValue: '公交', icon: 'fas fa-bus', type: '公交' },
          { dictValue: '自驾', icon: 'fas fa-car', type: '自驾' }
        ];
        console.log(dictData)
        transportMappings.forEach(mapping => {
          const item = dictData.find(dict => dict.dictValue === mapping.dictValue);
          if (item && item.dictValue) {
            transportItems.push({
              icon: mapping.icon,
              type: mapping.type,
              description: item.dictKey
            });
          }
        });

        if (transportItems.length > 0) {
          this.transportInfo = transportItems;
          console.log('提取的交通信息:', this.transportInfo);
        } else {
          console.log('未找到交通信息数据，使用默认数据');
        }

      } catch (error) {
        console.error('提取交通信息失败:', error);
      }
    },

    /**
     * 从字典数据中提取住宿推荐信息
     */
    extractHotelInfo(dictData) {
      try {
        console.log('开始提取住宿推荐数据...');

        // 首先找到"住宿推荐"的ID
        const hotelParentItem = dictData.find(dict => dict.dictKey === '住宿推荐' || dict.dictValue === '住宿推荐');

        if (!hotelParentItem) {
          console.log('未找到住宿推荐父项');
          return;
        }

        console.log('找到住宿推荐父项:', hotelParentItem);
        const parentId = hotelParentItem.id;

        // 查找所有以"住宿推荐"ID为parentId的子项
        const hotelItems = dictData.filter(dict => dict.parentId === parentId);
        console.log('找到住宿推荐子项:', hotelItems);

        if (hotelItems.length > 0) {
          // 将子项转换为住宿推荐格式
          const hotels = hotelItems.map(item => ({
            name: item.dictValue,
            description: item.dictKey,
            phone: item.remark || '' // 如果有备注字段可以作为电话
          }));

          this.hotelList = hotels;
          console.log('提取的住宿推荐:', this.hotelList);
        } else {
          console.log('未找到住宿推荐子项数据，使用默认数据');
        }

      } catch (error) {
        console.error('提取住宿推荐失败:', error);
      }
    },

    /**
     * 从字典数据中提取重要提醒信息
     */
    extractImportantNotices(dictData) {
      try {
        console.log('开始提取重要提醒数据...');

        // 首先找到"重要提醒"的ID
        const noticeParentItem = dictData.find(dict => dict.dictKey === '重要提醒' || dict.dictValue === '重要提醒');

        if (!noticeParentItem) {
          console.log('未找到重要提醒父项');
          return;
        }

        console.log('找到重要提醒父项:', noticeParentItem);
        const parentId = noticeParentItem.id;

        // 查找所有以"重要提醒"ID为parentId的子项
        const noticeItems = dictData.filter(dict => dict.parentId === parentId);
        console.log('找到重要提醒子项:', noticeItems);

        if (noticeItems.length > 0) {
          // 将子项转换为重要提醒格式
          const notices = noticeItems.map(item => ({
            title: item.dictValue,    // 提醒标题
            content: item.dictKey     // 提醒内容
          }));

          this.importantNotices = notices;
          console.log('提取的重要提醒:', this.importantNotices);
        } else {
          console.log('未找到重要提醒子项数据，使用默认数据');
        }

      } catch (error) {
        console.error('提取重要提醒失败:', error);
      }
    },

    /**
     * 从字典数据中提取紧急联系信息
     */
    extractEmergencyContacts(dictData) {
      try {
        console.log('开始提取紧急联系数据...');

        // 首先找到"紧急联系"的ID
        const contactParentItem = dictData.find(dict => dict.dictKey === '紧急联系' || dict.dictValue === '紧急联系');

        if (!contactParentItem) {
          console.log('未找到紧急联系父项');
          return;
        }

        console.log('找到紧急联系父项:', contactParentItem);
        const parentId = contactParentItem.id;

        // 查找所有以"紧急联系"ID为parentId的子项
        const contactItems = dictData.filter(dict => dict.parentId === parentId);
        console.log('找到紧急联系子项:', contactItems);

        if (contactItems.length > 0) {
          // 将子项转换为紧急联系格式
          const contacts = contactItems.map(item => ({
            title: item.dictValue,    // 联系人类型
            contact: item.dictKey     // 联系方式
          }));

          this.emergencyContacts = contacts;
          console.log('提取的紧急联系:', this.emergencyContacts);
        } else {
          console.log('未找到紧急联系子项数据，使用默认数据');
        }

      } catch (error) {
        console.error('提取紧急联系失败:', error);
      }
    },

    /**
     * 从字典数据中提取会议信息
     */
    extractMeetingInfo(dictData) {
      try {
        // 根据图片中的字典配置提取信息
        const meetingLocationItem = dictData.find(item => item.dictKey === '会议地点');
        if (meetingLocationItem) {
          this.meetingInfo.meetingLocation = meetingLocationItem.dictValue;
        }

        const meetingNameItem = dictData.find(item => item.dictKey === '会议主标题');
        if (meetingNameItem) {
          this.meetingInfo.meetingName = meetingNameItem.dictValue;
        }

        const organizerItem = dictData.find(item => item.dictKey === '主办单位');
        if (organizerItem) {
          this.meetingInfo.organizer = organizerItem.dictValue;
        }

        const meetingTimeItem = dictData.find(item => item.dictKey === '会议时间');
        if (meetingTimeItem) {
          this.meetingInfo.meetingTime = meetingTimeItem.dictValue;
        }

        const participantCountItem = dictData.find(item => item.dictKey === '参会人数');
        if (participantCountItem) {
          this.meetingInfo.participantCount = participantCountItem.dictValue;
        }

        console.log('提取的会议信息:', this.meetingInfo);
      } catch (error) {
        console.error('提取会议信息失败:', error);
      }
    },

    /**
     * 加载参会指南PDF链接
     */
    async loadGuidePdfUrl() {
      try {
        console.log('开始获取参会指南PDF下载链接...');

        // 调用字典接口获取参会指南PDF的URL
        const response = await getDictionary({
          code: 'hy_url' // 字典编码
        });

        console.log('PDF链接字典API响应:', response);

        if (response && response.data && response.data.success) {
          const dictData = response.data.data;

          if (dictData && Array.isArray(dictData) && dictData.length > 0) {
            // 查找参会指南PDF的配置项
            const guideItem = dictData.find(item =>
              item.dictValue === '参会指南' ||
              item.dictKey === '参会指南' ||
              item.dictValue === '参会指南pdf' ||
              item.dictKey === '参会指南pdf'
            );

            if (guideItem) {
              // 获取PDF的URL，可能在dictKey或dictValue中
              const pdfUrl = guideItem.dictKey || guideItem.dictValue;

              if (pdfUrl && (pdfUrl.startsWith('http') || pdfUrl.startsWith('/'))) {
                this.guidePdfUrl = pdfUrl;
                console.log('成功获取PDF下载链接:', this.guidePdfUrl);
              } else {
                console.warn('PDF链接格式不正确:', pdfUrl);
              }
            } else {
              console.warn('未找到参会指南PDF配置');
            }
          } else {
            console.warn('PDF链接字典数据为空');
          }
        } else {
          console.warn('PDF链接字典API响应格式不正确');
        }

      } catch (error) {
        console.error('获取参会指南PDF链接失败:', error);
      }
    },

    /**
     * 加载指南数据
     */
    async loadGuidesData() {
      const startTime = Date.now();

      try {
        console.log('开始加载指南数据...');

        // 直接调用API
        const response = await getList(1, 20, {});
        console.log('指南API响应:', response);

        // 检查响应格式
        if (response && response.data && response.data.success) {
          // 使用数据转换器处理数据
          const transformedData = dataTransformers.guides(response.data);
          console.log('转换后的指南数据:', transformedData);

          if (transformedData && transformedData.length > 0) {
            this.guides = transformedData;
          }
          this.dataSource = 'api';
          this.hasError = false;
          this.errorMessage = '';
        } else {
          throw new Error('API响应格式不正确');
        }

        this.responseTime = Date.now() - startTime;

      } catch (error) {
        console.error('加载指南数据失败:', error);
        this.guides = this.defaultGuidesData;
        this.dataSource = 'fallback';
        this.hasError = true;
        this.errorMessage = error.message || '数据加载失败，使用默认数据';
        this.responseTime = Date.now() - startTime;
      }
    },

    /**
     * 加载议程数据
     */
    async loadAgendaData() {
      try {
        console.log('开始加载议程数据...');

        // 先使用默认数据，确保方法能正常工作
        this.agendaList = this.getDefaultAgendaData();
        console.log('使用默认议程数据，共', this.agendaList.length, '条记录');

        // 尝试调用议程API
        try {
          const response = await getAgendaList(1, 50, {});
          console.log('议程API响应:', response);

          // 检查响应格式
          if (response && response.data && response.data.success) {
            // 使用数据转换器处理数据
            const transformedData = dataTransformers.agenda(response.data);
            console.log('转换后的议程数据:', transformedData);

            if (transformedData && transformedData.length > 0) {
              this.agendaList = transformedData;
              console.log('API议程数据加载成功，共', transformedData.length, '条记录');
            }
          }
        } catch (apiError) {
          console.warn('议程API调用失败，继续使用默认数据:', apiError);
        }

      } catch (error) {
        console.error('加载议程数据失败:', error);
        // 确保至少有默认数据
        this.agendaList = this.getDefaultAgendaData();
      }
    },

    /**
     * 获取默认议程数据
     */
    getDefaultAgendaData() {
      return [
        { id: 1, time: '08:30-09:00', topic: '签到注册', speaker: '会务组', venue: '主会场大厅', description: '参会者签到，领取会议资料', date: '2025-09-15' },
        { id: 2, time: '09:00-09:30', topic: '开幕式', speaker: '主办方领导', venue: '主会场', description: '大会开幕致辞，嘉宾介绍', date: '2025-09-15' },
        { id: 3, time: '09:30-10:30', topic: '主题演讲：数智攀登，管理跃升', speaker: '公司领导', venue: '主会场', description: '探讨数字化智能化在企业管理中的创新应用', date: '2025-09-15' },
        { id: 4, time: '10:30-10:45', topic: '茶歇', speaker: '', venue: '休息区', description: '休息时间，自由交流', date: '2025-09-15' },
        { id: 5, time: '10:45-12:00', topic: '圆桌讨论：企业管理创新实践', speaker: '各部门负责人', venue: '主会场', description: '管理层共同探讨企业管理现代化转型路径', date: '2025-09-15' },
        { id: 6, time: '12:00-13:30', topic: '午餐时间', speaker: '', venue: '餐厅', description: '自助午餐，网络交流', date: '2025-09-15' },
        { id: 7, time: '09:00-10:00', topic: '经验分享：数字化管理实践', speaker: '技术专家', venue: '主会场', description: '分享数字化转型的实践经验', date: '2025-09-16' },
        { id: 8, time: '10:00-11:00', topic: '现场参观', speaker: '导览员', venue: '生产车间', description: '参观智能化生产线', date: '2025-09-16' },
        { id: 9, time: '11:00-12:00', topic: '闭幕式', speaker: '主办方领导', venue: '主会场', description: '大会总结，颁奖典礼', date: '2025-09-16' }
      ];
    },

    /**
     * 提取开始时间用于排序
     */
    extractStartTime(timeStr) {
      if (!timeStr) return '00:00:00';

      // 处理 "08:30:00-09:00:00" 或 "08:30-09:00" 格式
      const startTime = timeStr.split('-')[0].trim();

      // 确保时间格式为 HH:MM:SS
      if (startTime.length === 5) {
        return startTime + ':00'; // 08:30 -> 08:30:00
      }

      return startTime || '00:00:00';
    },

    /**
     * 格式化日期显示
     */
    formatDate(dateStr) {
      if (!dateStr) return '会议议程';

      try {
        const date = new Date(dateStr);
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();

        // 获取所有日期并排序，动态确定是第几天
        const allDates = [...new Set(this.agendaList.map(item => item.date))].sort();
        const dayIndex = allDates.indexOf(dateStr);

        if (dayIndex >= 0) {
          const dayNumber = dayIndex + 1;
          return `第${dayNumber}天 - ${month}月${day}日`;
        } else {
          return `${month}月${day}日`;
        }
      } catch (error) {
        return dateStr;
      }
    },

    /**
     * 切换TAB
     */
    switchTab(tabName) {
      this.activeTab = tabName;
      console.log('切换到TAB:', tabName);
    },

    /**
     * 打开地图
     */
    openMap() {
      if (this.$message) {
        this.$message.info('地图导航功能');
      } else {
        alert('地图导航\n（演示功能）\n将打开地图应用导航到会议地点');
      }
    },

    /**
     * 下载完整指南
     */
    async downloadGuide() {
      try {
        console.log('开始获取参会指南PDF下载链接...');

        // 调用字典接口获取参会指南PDF的URL
        const response = await getDictionary({
          code: 'hy_url' // 字典编码
        });

        console.log('字典API响应:', response);

        if (response && response.data && response.data.success) {
          const dictData = response.data.data;

          if (dictData && Array.isArray(dictData) && dictData.length > 0) {
            // 查找参会指南PDF的配置项
            const guideItem = dictData.find(item =>
              item.dictValue === '参会指南' ||
              item.dictKey === '参会指南' ||
              item.dictValue === '参会指南pdf' ||
              item.dictKey === '参会指南pdf'
            );

            if (guideItem) {
              // 获取PDF的URL，可能在dictKey或dictValue中
              const pdfUrl = guideItem.dictKey || guideItem.dictValue;

              if (pdfUrl && (pdfUrl.startsWith('http') || pdfUrl.startsWith('/'))) {
                console.log('找到PDF下载链接:', pdfUrl);

                // 创建下载链接
                const link = document.createElement('a');
                link.href = pdfUrl;
                link.download = '参会指南.pdf'; // 设置下载文件名
                link.target = '_blank'; // 在新窗口打开

                // 触发下载
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                if (this.$message) {
                  this.$message.success('参会指南下载已开始！');
                } else {
                  alert('参会指南下载已开始！');
                }
              } else {
                throw new Error('PDF链接格式不正确');
              }
            } else {
              throw new Error('未找到参会指南PDF配置');
            }
          } else {
            throw new Error('字典数据为空');
          }
        } else {
          throw new Error('字典API响应格式不正确');
        }

      } catch (error) {
        console.error('下载参会指南失败:', error);

        if (this.$message) {
          this.$message.error(`下载失败: ${error.message}`);
        } else {
          alert(`下载失败: ${error.message}`);
        }
      }
    }
  }
}
</script>
<style scoped>
/* 页面通用样式 */
.page-container {
    max-width: 375px;
    margin: 0 auto;
    min-height: 100vh;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%);
}

.page-content {
    margin-top: 20px;
    min-height: calc(100vh - 80px);
}

.list-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    animation: slideInUp 0.6s ease forwards;
}

.form-header {
    text-align: center;
    margin-bottom: 30px;
}

.form-header i {
    font-size: 48px;
    color: #4682B4;
    margin-bottom: 15px;
}

.form-header h2 {
    color: #333;
    font-size: 24px;
    margin-bottom: 10px;
}

.form-header p {
    color: #666;
    font-size: 14px;
}

/* TAB导航样式 */
.guide-categories {
    display: flex;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    padding: 5px;
    margin: 20px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.guide-tab {
    flex: 1;
    background: none;
    border: none;
    padding: 10px;
    border-radius: 8px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #666;
}

.guide-tab.active {
    background: #4682B4;
    color: white;
}

.guide-tab:hover {
    background: rgba(70, 130, 180, 0.1);
    color: #4682B4;
}

.guide-tab.active:hover {
    background: #4682B4;
    color: white;
}

/* 内容区域样式 */
.guide-content {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.guide-section {
    margin-bottom: 25px;
}

.guide-section h3 {
    color: #333;
    font-size: 16px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.guide-section h3 i {
    color: #4682B4;
}

/* 基本信息样式 */
.info-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.info-row {
    display: flex;
    margin-bottom: 12px;
    align-items: flex-start;
}

.info-row:last-child {
    margin-bottom: 0;
}

.info-row strong {
    color: #333;
    min-width: 80px;
    font-size: 14px;
}

.info-row span {
    color: #666;
    font-size: 14px;
    flex: 1;
}

.participant-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

.participant-item {
    background: white;
    border-radius: 10px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.participant-item i {
    color: #4682B4;
    font-size: 16px;
}

/* 日程安排样式 */
.schedule-day {
    margin-bottom: 20px;
}

.schedule-day h4 {
    color: #4682B4;
    font-size: 14px;
    margin-bottom: 10px;
    padding: 8px 12px;
    background: rgba(70, 130, 180, 0.1);
    border-radius: 8px;
}

.schedule-list {
    background: white;
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.schedule-item {
    display: flex;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.schedule-item:last-child {
    border-bottom: none;
}

.schedule-item .time {
    color: #4682B4;
    font-size: 12px;
    font-weight: 500;
    min-width: 80px;
}

.schedule-item .content {
    color: #333;
    font-size: 13px;
    flex: 1;
}

.schedule-item .topic {
  margin-left:5px;
    font-weight: 500;
    margin-bottom: 4px;
    color: #333;
}

.schedule-item .speaker,
.schedule-item .venue {
    font-size: 11px;
    color: #666;
    margin: 2px 0;
    display: flex;
    align-items: center;
    gap: 4px;
}

.schedule-item .speaker i,
.schedule-item .venue i {
    font-size: 10px;
    color: #4682B4;
    width: 12px;
}

/* 无数据样式 */
.no-data {
    text-align: center;
    padding: 40px 20px;
    color: #999;
}

.no-data i {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 15px;
}

.no-data p {
    font-size: 14px;
    margin: 0;
}

/* 地点卡片样式 */
.location-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.location-info h4 {
    color: #333;
    font-size: 16px;
    margin-bottom: 8px;
}

.location-info p {
    color: #666;
    font-size: 13px;
    margin: 3px 0;
}

.map-btn {
    background: #4682B4;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
}

.map-btn:hover {
    background: #357abd;
    transform: translateY(-1px);
}

/* 交通选项样式 */
.transport-options {
    background: white;
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.transport-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
}

.transport-item:last-child {
    border-bottom: none;
}

.transport-item i {
    color: #4682B4;
    font-size: 18px;
    margin-top: 2px;
}

.transport-item strong {
    color: #333;
    font-size: 14px;
    display: block;
    margin-bottom: 3px;
}

.transport-item p {
    color: #666;
    font-size: 12px;
    margin: 0;
}



/* 酒店列表样式 */
.hotel-list {
    background: white;
    border-radius: 12px;
    padding: 0px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.hotel-item {
  
    background: #f8f9fa;
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 0px;
}

.hotel-item:last-child {
    margin-bottom: 0;
}

.hotel-item h4 {
    color: #333;
    margin-bottom: 8px;
}

.hotel-item p {
    color: #666;
    font-size: 12px;
    margin: 3px 0;
}



/* 会议须知样式 */
.rules-list {
    background: white;
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.rule-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
}

.rule-item:last-child {
    border-bottom: none;
}

.rule-item i {
    color: #4682B4;
    font-size: 18px;
    margin-top: 2px;
}

.rule-item strong {
    color: #333;
    font-size: 14px;
    display: block;
    margin-bottom: 3px;
}

.rule-item p {
    color: #666;
    font-size: 12px;
    margin: 0;
}



/* 紧急联系样式 */
.contact-emergency {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.emergency-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.emergency-item:last-child {
    border-bottom: none;
}

.emergency-item strong {
    color: #333;
    font-size: 14px;
}

.emergency-item span {
    color: #4682B4;
    font-size: 14px;
    font-weight: 500;
}

/* 下载按钮样式 */
.download-section {
    text-align: center;
    margin-top: 30px;
}

.submit-btn {
    background: #4682B4;
    color: white;
    border: none;
    border-radius: 12px;
    padding: 15px 30px;
    font-size: 16px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
}

.submit-btn:hover {
    background: #357abd;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(70, 130, 180, 0.4);
}

.submit-btn i {
    font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 480px) {


    .list-container {
        padding: 15px;
    }

    .guide-tab {
        font-size: 11px;
        padding: 8px;
    }

    .location-card {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .hotel-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .meal-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .rule-item {
        padding: 15px;
    }

    .guide-section h3 {
        font-size: 14px;
    }
}
</style>