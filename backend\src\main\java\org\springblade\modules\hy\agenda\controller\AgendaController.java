/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.agenda.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.hy.agenda.pojo.entity.AgendaEntity;
import org.springblade.modules.hy.agenda.pojo.vo.AgendaVO;
import org.springblade.modules.hy.agenda.excel.AgendaExcel;
import org.springblade.modules.hy.agenda.excel.AgendaImportExcel;
import org.springblade.modules.hy.agenda.excel.AgendaImporter;
import org.springblade.modules.hy.agenda.wrapper.AgendaWrapper;
import org.springblade.modules.hy.agenda.service.IAgendaService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

/**
 * 会议议程表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@RestController
@AllArgsConstructor
@RequestMapping("hy/agenda")
@Tag(name = "会议议程管理", description = "会议议程信息的增删改查操作，包括议程详情查询、分页列表、新增修改、删除和数据导出等功能")
public class AgendaController extends BladeController {

	private final IAgendaService agendaService;

	/**
	 * 会议议程详情查询
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "获取会议议程详情", description = "根据议程ID或其他条件查询单个会议议程的详细信息")
	@ApiResponses({
		@ApiResponse(responseCode = "200", description = "查询成功"),
		@ApiResponse(responseCode = "400", description = "请求参数错误"),
		@ApiResponse(responseCode = "500", description = "服务器内部错误")
	})
	public R<AgendaVO> detail(@Parameter(description = "会议议程实体对象，包含查询条件", required = true) AgendaEntity agenda) {
		AgendaEntity detail = agendaService.getOne(Condition.getQueryWrapper(agenda));
		return R.data(AgendaWrapper.build().entityVO(detail));
	}
	/**
	 * 会议议程分页查询
	 */
	@GetMapping("/list")
	@Parameters({
		@Parameter(name = "title", description = "议程标题", in = ParameterIn.QUERY, schema = @Schema(type = "string")),
		@Parameter(name = "speaker", description = "演讲者", in = ParameterIn.QUERY, schema = @Schema(type = "string")),
		@Parameter(name = "startTime", description = "开始时间", in = ParameterIn.QUERY, schema = @Schema(type = "string", format = "date-time")),
		@Parameter(name = "endTime", description = "结束时间", in = ParameterIn.QUERY, schema = @Schema(type = "string", format = "date-time")),
		@Parameter(name = "current", description = "当前页码", in = ParameterIn.QUERY, schema = @Schema(type = "integer", defaultValue = "1")),
		@Parameter(name = "size", description = "每页条数", in = ParameterIn.QUERY, schema = @Schema(type = "integer", defaultValue = "10"))
	})
	@ApiOperationSupport(order = 2)
	@Operation(summary = "会议议程分页列表", description = "分页查询会议议程信息，支持按标题、演讲者、时间等条件筛选")
	@ApiResponses({
		@ApiResponse(responseCode = "200", description = "查询成功"),
		@ApiResponse(responseCode = "400", description = "请求参数错误"),
		@ApiResponse(responseCode = "500", description = "服务器内部错误")
	})
	public R<IPage<AgendaVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> agenda,
									@Parameter(description = "分页查询参数") Query query) {
		IPage<AgendaEntity> pages = agendaService.page(Condition.getPage(query), Condition.getQueryWrapper(agenda, AgendaEntity.class));
		return R.data(AgendaWrapper.build().pageVO(pages));
	}

	/**
	 * 会议议程自定义分页查询
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "会议议程自定义分页", description = "使用自定义SQL进行会议议程分页查询，支持复杂条件筛选")
	@ApiResponses({
		@ApiResponse(responseCode = "200", description = "查询成功"),
		@ApiResponse(responseCode = "400", description = "请求参数错误"),
		@ApiResponse(responseCode = "500", description = "服务器内部错误")
	})
	public R<IPage<AgendaVO>> page(@Parameter(description = "会议议程查询条件对象") AgendaVO agenda,
									@Parameter(description = "分页查询参数") Query query) {
		IPage<AgendaVO> pages = agendaService.selectAgendaPage(Condition.getPage(query), agenda);
		return R.data(pages);
	}

	/**
	 * 新增会议议程
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增会议议程", description = "创建新的会议议程信息，包括议程标题、时间、演讲者等详细信息")
	@ApiResponses({
		@ApiResponse(responseCode = "200", description = "新增成功"),
		@ApiResponse(responseCode = "400", description = "请求参数错误或数据验证失败"),
		@ApiResponse(responseCode = "500", description = "服务器内部错误")
	})
	public R save(@Parameter(description = "会议议程信息对象，包含所有必要的议程数据", required = true)
				  @Valid @RequestBody AgendaEntity agenda) {
		return R.status(agendaService.save(agenda));
	}

	/**
	 * 修改会议议程
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改会议议程", description = "根据议程ID更新会议议程信息，包括议程标题、时间、演讲者等详细信息")
	@ApiResponses({
		@ApiResponse(responseCode = "200", description = "修改成功"),
		@ApiResponse(responseCode = "400", description = "请求参数错误或数据验证失败"),
		@ApiResponse(responseCode = "404", description = "议程不存在"),
		@ApiResponse(responseCode = "500", description = "服务器内部错误")
	})
	public R update(@Parameter(description = "会议议程信息对象，必须包含有效的ID", required = true)
					@Valid @RequestBody AgendaEntity agenda) {
		return R.status(agendaService.updateById(agenda));
	}

	/**
	 * 新增或修改会议议程
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改会议议程", description = "智能判断是新增还是修改操作，如果包含ID则修改，否则新增")
	@ApiResponses({
		@ApiResponse(responseCode = "200", description = "操作成功"),
		@ApiResponse(responseCode = "400", description = "请求参数错误或数据验证失败"),
		@ApiResponse(responseCode = "500", description = "服务器内部错误")
	})
	public R submit(@Parameter(description = "会议议程信息对象，包含ID时为修改，不包含ID时为新增", required = true)
					@Valid @RequestBody AgendaEntity agenda) {
		return R.status(agendaService.saveOrUpdate(agenda));
	}

	/**
	 * 删除会议议程
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "删除会议议程", description = "根据议程ID列表逻辑删除会议议程，支持批量删除")
	@ApiResponses({
		@ApiResponse(responseCode = "200", description = "删除成功"),
		@ApiResponse(responseCode = "400", description = "请求参数错误"),
		@ApiResponse(responseCode = "404", description = "议程不存在"),
		@ApiResponse(responseCode = "500", description = "服务器内部错误")
	})
	public R remove(@Parameter(description = "议程ID集合，多个ID用逗号分隔", required = true, example = "1,2,3")
					@RequestParam String ids) {
		return R.status(agendaService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出会议议程数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-agenda")
	@Parameters({
		@Parameter(name = "title", description = "议程标题", in = ParameterIn.QUERY, schema = @Schema(type = "string")),
		@Parameter(name = "speaker", description = "演讲者", in = ParameterIn.QUERY, schema = @Schema(type = "string")),
		@Parameter(name = "startTime", description = "开始时间", in = ParameterIn.QUERY, schema = @Schema(type = "string", format = "date-time")),
		@Parameter(name = "endTime", description = "结束时间", in = ParameterIn.QUERY, schema = @Schema(type = "string", format = "date-time"))
	})
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出会议议程数据", description = "导出会议议程数据到Excel文件，支持按条件筛选导出")
	@ApiResponses({
		@ApiResponse(responseCode = "200", description = "导出成功"),
		@ApiResponse(responseCode = "400", description = "请求参数错误"),
		@ApiResponse(responseCode = "403", description = "权限不足，需要管理员权限"),
		@ApiResponse(responseCode = "500", description = "服务器内部错误")
	})
	public void exportAgenda(@Parameter(hidden = true) @RequestParam Map<String, Object> agenda,
							@Parameter(hidden = true) BladeUser bladeUser,
							@Parameter(hidden = true) HttpServletResponse response) {
		QueryWrapper<AgendaEntity> queryWrapper = Condition.getQueryWrapper(agenda, AgendaEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(Agenda::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(AgendaEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<AgendaExcel> list = agendaService.exportAgenda(queryWrapper);
		ExcelUtil.export(response, "会议议程表数据" + DateUtil.time(), "会议议程表数据表", list, AgendaExcel.class);
	}

	/**
	 * 导入会议议程数据
	 */
	@PostMapping("import-agenda")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "导入会议议程", description = "传入excel文件导入会议议程数据")
	@ApiResponses({
		@ApiResponse(responseCode = "200", description = "导入成功"),
		@ApiResponse(responseCode = "400", description = "请求参数错误或文件格式错误"),
		@ApiResponse(responseCode = "403", description = "权限不足，需要管理员权限"),
		@ApiResponse(responseCode = "500", description = "服务器内部错误")
	})
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	public R importAgenda(@Parameter(description = "Excel文件", required = true) MultipartFile file) {
		AgendaImporter agendaImporter = new AgendaImporter(agendaService, false);
		ExcelUtil.save(file, agendaImporter, AgendaImportExcel.class);
		return R.success("操作成功");
	}

	/**
	 * 导出会议议程模板
	 */
	@GetMapping("export-template")
	@ApiOperationSupport(order = 11)
	@Operation(summary = "导出会议议程模板", description = "下载会议议程导入模板")
	@ApiResponses({
		@ApiResponse(responseCode = "200", description = "导出成功"),
		@ApiResponse(responseCode = "500", description = "服务器内部错误")
	})
	public void exportTemplate(@Parameter(hidden = true) HttpServletResponse response) {
		List<AgendaImportExcel> list = new ArrayList<>();
		ExcelUtil.export(response, "会议议程导入模板", "会议议程表", list, AgendaImportExcel.class);
	}

}
