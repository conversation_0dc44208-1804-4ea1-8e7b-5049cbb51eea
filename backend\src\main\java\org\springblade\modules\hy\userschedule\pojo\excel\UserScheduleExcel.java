/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.userschedule.pojo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

/**
 * 用户日程信息表 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class UserScheduleExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	@ExcelProperty("用户ID")
	private Long userId;

	@ExcelProperty("用户名称")
	private String userName;

	@ExcelProperty("用户真实姓名")
	private String userRealName;

	@ExcelProperty("用户邮箱")
	private String userEmail;

	@ExcelProperty("用户手机号")
	private String userPhone;

	@ExcelProperty("用户工号")
	private String userEmployeeNumber;

	@ExcelProperty("用户房号")
	private String userRoomNumber;

	@ExcelProperty("用户会议座位号")
	private String userMeetingSeatNumber;

	@ExcelProperty("日程信息")
	@ColumnWidth(50)
	private String scheduleContent;

	@ExcelProperty("用餐信息")
	@ColumnWidth(50)
	private String diningInfo;

	@ExcelProperty("住宿信息")
	@ColumnWidth(50)
	private String accommodationInfo;

}
