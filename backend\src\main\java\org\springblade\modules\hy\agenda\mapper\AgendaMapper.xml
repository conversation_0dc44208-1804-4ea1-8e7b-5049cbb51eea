<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.hy.agenda.mapper.AgendaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="agendaResultMap" type="org.springblade.modules.hy.agenda.pojo.entity.AgendaEntity">
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="topic" property="topic"/>
        <result column="speaker" property="speaker"/>
        <result column="venue" property="venue"/>
        <result column="description" property="description"/>
        <result column="create_user" property="createUser"/>
        <result column="id" property="id"/>
        <result column="date" property="date"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectAgendaPage" resultMap="agendaResultMap">
        select * from hy_agenda where is_deleted = 0
    </select>


    <select id="exportAgenda" resultType="org.springblade.modules.hy.agenda.excel.AgendaExcel">
        SELECT
            TO_CHAR(start_time, 'HH24:MI') as startTime,
            TO_CHAR(end_time, 'HH24:MI') as endTime,
            topic,
            speaker,
            venue,
            description,
            id,
            TO_CHAR(date, 'YYYY-MM-DD') as date,
            is_deleted as isDeleted
        FROM hy_agenda ${ew.customSqlSegment}
    </select>

</mapper>
